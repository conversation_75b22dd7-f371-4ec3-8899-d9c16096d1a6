<!-- ********** Overview page ********** -->
<section class="c-page m-overview" aria-labelledby="overview-heading">

{{#if loading}}
    <!-- Loading state -->
    <div class="c-loading-overlay">
        <div class="c-loading-spinner">Loading overview data...</div>
    </div>
{{else if error}}
    <!-- Error state -->
    <div class="c-error-state">
        <p>Unable to load overview data. Please refresh the page and try again.</p>
        <button onclick="window.location.reload()">Refresh Page</button>
    </div>
{{else}}
    <!-- Content loaded or fallback -->

    <!-- Column 1 – Appointments & Bids  -->
    <article class="c-p-column">
        <!-- Appointments card -->
        <div class="c-card">
            <header class="c-card-header">
                <h3>Upcoming Appointments</h3>
                <a class="c-see-all" href="#/appointments">See All →</a>
            </header>
            {{#if appointments.length}}
                <div class="c-card-content">
                    {{#each appointments}}
                        <div class="c-appointment-item">
                            <div class="c-ai-date">
                                <div class="c-aid-month">{{formatMonth scheduledStart}}</div>
                                <div class="c-aid-day">{{formatDay scheduledStart}}</div>
                            </div>
                            <div class="c-ai-details">
                                <div class="c-aid-type">{{type}}</div>
                                <div class="c-aid-time">{{formatTime scheduledStart}} - {{formatTime scheduledEnd}}</div>
                                <div class="c-aid-location">{{location}}</div>
                            </div>
                            <div class="c-ai-status">
                                <span class="c-ais-badge {{statusClass status}}">{{status}}</span>
                            </div>
                        </div>
                    {{/each}}
                </div>
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image">
                        <use
                            xlink:href="#module--customer-portal--appointments"
                        ></use></svg>
                    <p class="title">No Appointments</p>
                    <p class="subtitle">
                        It looks like your calendar is wide open —<br />
                        no appointments on the horizon!
                    </p>
                </div>
            {{/if}}
        </div>

        <!-- Bids card -->
        <div class="c-card">
            <header class="c-card-header">
                <h3>Open Bids</h3>
                <a class="c-see-all" href="#/bids">See All →</a>
            </header>
            {{#if bids.length}}
                <div class="c-card-content">
                    {{#each bids}}
                        <div class="c-bid-item">
                            <div class="c-bi-header">
                                <div class="c-bih-title">{{projectName}}</div>
                                <div class="c-bih-id">#{{bidId}}</div>
                            </div>
                            <div class="c-bi-details">
                                <div class="c-bid-address">{{address}}</div>
                                <div class="c-bid-meta">
                                    <span class="c-bm-total">Bid Total: <strong>${{formatCurrency total}}</strong></span>
                                </div>
                            </div>
                            <div class="c-bi-actions">
                                <button class="c-bia-btn c-bia-view" data-bid-id="{{bidId}}">View Bid</button>
                                <button class="c-bia-btn c-bia-download" data-bid-id="{{bidId}}">
                                    <svg class="c-bia-icon" width="16" height="16" viewBox="0 0 16 16">
                                        <path d="M8 12l-4-4h3V4h2v4h3l-4 4z"/>
                                        <path d="M2 14h12v2H2z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    {{/each}}
                </div>
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image"><use
                            xlink:href="#module--customer-portal--bids"
                        ></use></svg>
                    <p class="title">No Bids</p>
                    <p class="subtitle">
                        It looks like there are currently no bids placed.
                    </p>
                </div>
            {{/if}}
        </div>
    </article>

    <!-- Column 2 – Invoices -->
    <article class="c-p-column">
        <div class="c-card">
            <header class="c-card-header">
                <h3>Open Invoices</h3>
                <a class="c-see-all" href="#/invoices">See All →</a>
            </header>
            {{#if invoices.length}}
                <div class="c-card-content">
                    {{#each invoices}}
                        <div class="c-invoice-item">
                            <div class="c-ii-header">
                                <div class="c-iih-type">{{type}}</div>
                                <div class="c-iih-status">
                                    <span class="c-iis-badge {{statusClass status}}">{{status}}</span>
                                </div>
                            </div>
                            <div class="c-ii-details">
                                <div class="c-iid-date">Due {{formatDate dueDate}}</div>
                                <div class="c-iid-amounts">
                                    <div class="c-iida-total">Invoice Total: <strong>${{formatCurrency total}}</strong></div>
                                    <div class="c-iida-remaining">Remaining Balance: <strong>${{formatCurrency remainingBalance}}</strong></div>
                                </div>
                                <div class="c-iid-number">Invoice #{{invoiceNumber}}</div>
                            </div>
                        </div>
                    {{/each}}
                </div>
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image"><use
                            xlink:href="#module--customer-portal--invoices"
                        ></use></svg>
                    <p class="title">No Invoices</p>
                    <p class="subtitle">
                        Your invoice inbox is as empty as a desert! Nothing to pay.
                    </p>
                </div>
            {{/if}}
        </div>
    </article>

    <!-- Column 3 – Recent Uploads -->
    <article class="c-p-column">
        <div class="c-card">
            <header class="c-card-header">
                <h3>Recent Uploads</h3>
                <a class="c-see-all" href="#/gallery">See All →</a>
            </header>
            {{#if uploads.length}}
                <div class="c-card-content">
                    <div class="c-uploads-grid">
                        {{#each uploads}}
                            <div class="c-upload-item">
                                <div class="c-ui-thumbnail">
                                    <img src="{{thumbnailUrl}}" alt="{{filename}}" loading="lazy" />
                                    <div class="c-ui-overlay">
                                        <span class="c-uio-date">{{formatDate uploadDate}}</span>
                                    </div>
                                </div>
                            </div>
                        {{/each}}
                    </div>
                </div>
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image"><use
                            xlink:href="#module--customer-portal--uploads"
                        ></use></svg>
                    <p class="title">No Images</p>
                    <p class="subtitle">
                        It seems there are no updates to share at this time.
                    </p>
                </div>
            {{/if}}
        </div>
    </article>

{{/if}}
<!-- End conditional content -->

</section>