<!-- ********** Overview page ********** -->
<section class="c-page m-overview" aria-labelledby="overview-heading">

{{#if loading}}
    <!-- Loading state -->
    <div class="c-loading-overlay">
        <div class="c-loading-spinner">Loading overview data...</div>
    </div>
{{else if error}}
    <!-- Error state -->
    <div class="c-error-state">
        <p>Unable to load overview data. Please refresh the page and try again.</p>
        <button onclick="window.location.reload()">Refresh Page</button>
    </div>
{{else}}
    <!-- Content loaded or fallback -->

    <!-- Column 1 – Appointments & Bids  -->
    <article class="c-p-column">
        <!-- Appointments card -->
        <div class="c-card">
            <header class="c-card-header">
                <h3>Upcoming Appointments</h3>
                <a class="c-see-all" href="#">See All →</a>
            </header>
            {{#if appointments.length}}
                {{#each appointments}}
                    <p>{{scheduledStart}} - {{scheduledEnd}}</p>
                {{/each}}
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image">
                        <use
                            xlink:href="#module--customer-portal--appointments"
                        ></use></svg>
                    <p class="title">No Appointments</p>
                    <p class="subtitle">
                        It looks like your calendar is wide open —<br />
                        no appointments on the horizon!
                    </p>
                </div>
            {{/if}}
        </div>

        <!-- Bids card -->
        <div class="c-card">
            <header class="c-card-header">
                <h3>Open Bids</h3>
                <a class="c-see-all" href="#">See All →</a>
            </header>
            {{#if bids.length}}
                {{#each bids}}
                    <p>{{this.name}} - {{this.status}}</p>
                {{/each}}
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image"><use
                            xlink:href="#module--customer-portal--bids"
                        ></use></svg>
                    <p class="title">No Bids</p>
                    <p class="subtitle">
                        It looks like there are currently no bids placed.
                    </p>
                </div>
            {{/if}}
        </div>
    </article>

    <!-- Column 2 – Invoices -->
    <article class="c-p-column">
        <div class="c-card">
            <header class="c-card-header">
                <h3>Open Invoices</h3>
                <a class="c-see-all" href="#">See All →</a>
            </header>
            {{#if invoices.length}}
                {{#each invoices}}
                    <p>{{this.number}} - {{this.amount}}</p>
                {{/each}}
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image"><use
                            xlink:href="#module--customer-portal--invoices"
                        ></use></svg>
                    <p class="title">No Invoices</p>
                    <p class="subtitle">
                        Your invoice inbox is as empty as a desert! Nothing to pay.
                    </p>
                </div>
            {{/if}}
        </div>
    </article>

    <!-- Column 3 – Recent Uploads -->
    <article class="c-p-column">
        <div class="c-card">
            <header class="c-card-header">
                <h3>Recent Uploads</h3>
                <a class="c-see-all" href="#">See All →</a>
            </header>
            {{#if uploads.length}}
                {{#each uploads}}
                    <p>{{this.filename}} - {{this.uploadDate}}</p>
                {{/each}}
            {{else}}
                <div class="c-empty">
                    <svg class="c-sbciw-image"><use
                            xlink:href="#module--customer-portal--uploads"
                        ></use></svg>
                    <p class="title">No Images</p>
                    <p class="subtitle">
                        It seems there are no updates to share at this time.
                    </p>
                </div>
            {{/if}}
        </div>
    </article>

{{/if}}
<!-- End conditional content -->

</section>