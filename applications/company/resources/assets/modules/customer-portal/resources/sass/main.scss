@use "~@cac-sass/config/global" with (
    $legacy: false
);
@use "~@cac-sass/base";
@use "~@cac-sass/app/form";
@use "~@cas-layout-sass/layout";

.a-pic-logo,
.a-p-image {
    display: none;
}

:root {
    /* brand */
    --blue‑50: #e9f1ff;
    --blue‑500: #2970ff;

    /* greys */
    --gray‑25: #fafbfd;
    --gray‑50: #f4f6fa;
    --gray‑100: #e5e9f0;
    --gray‑300: #c9d2e1;
    --gray‑500: #6b7280;
    --gray‑700: #344155;

    --radius‑pill: 9999px;
}

/* ---------- Reset / Base ---------- */
*,
*::before,
*::after {
    box-sizing: border-box;
}
html {
    font-family:
        "Inter",
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        <PERSON><PERSON>,
        "Helvetica Neue",
        Arial,
        sans-serif;
    line-height: 1.4;
    font-size: 14px;
    color: var(--gray‑700);
    scroll-behavior: smooth;
}
body {
    margin: 0;
    background: var(--gray‑50);
}
a {
    color: var(--blue‑500);
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
img {
    display: block;
    max-width: 100%;
    height: auto;
}
button {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
}

.m-customer-portal {
    background: #fff;

    border-radius: 104px var(--page-radius, 12px) var(--page-radius, 12px)
        var(--page-radius, 12px);
    margin: 24px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 32px);
    overflow: hidden;

    .t-hidden {
        display: none;
    }

    .c-header {
        display: flex;
        align-items: flex-start;
        gap: 0;
        padding: 24px 24px 0 24px;
        background: #fff;

        .c-h-logo {
            width: base.unit-rem-calc(320px);
            height: base.unit-rem-calc(160px);
            padding: base.unit-rem-calc(24px);
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 1000px;
            border: 1px solid var(--gray-light-4, #e1e8f0);
            background: #fff;
            margin-bottom: base.unit-rem-calc(-78px);
            z-index: 10;
            flex-shrink: 0;
            display: flex;
        }

        .c-h-navigation {
            display: flex;
            width: 100%;
            gap: base.unit-rem-calc(8px);
            justify-content: flex-start;
            align-items: center;
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            overflow-x: auto;
            margin-right: base.unit-rem-calc(-28px);
            margin-top: base.unit-rem-calc(28px);
            border-bottom: 1px solid var(--gray-light-4, #e1e8f0);
            &::before {
                content: "";
                position: absolute;
                right: base.unit-rem-calc(-8px);
                top: 0;
                width: base.unit-rem-calc(16px);
                height: base.unit-rem-calc(56px);
                background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0%,
                    #ffffff 60%
                );
            }
            &::after {
                content: "";
                position: absolute;
                left: base.unit-rem-calc(-8px);
                top: 0;
                width: base.unit-rem-calc(16px);
                height: base.unit-rem-calc(56px);
                background: linear-gradient(
                    270deg,
                    rgba(255, 255, 255, 0) 0%,
                    #ffffff 60%
                );
            }
            @include base.respond-to("<small") {
                margin: 0;
                &::before {
                    right: 0;
                }
                &::after {
                    left: 0;
                }
            }
            .c-hn-item {
                cursor: pointer;
                height: base.unit-rem-calc(40px);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: base.unit-rem-calc(8px);
                border-radius: base.unit-rem-calc(40px);
                padding: 0 base.unit-rem-calc(12px);
                transition:
                    background-color 0.35s linear,
                    padding 0.25s ease-in-out;
                @include base.respond-to("hover") {
                    &:hover {
                        padding: 0 base.unit-rem-calc(24px);
                        background-color: #edf1f7;
                    }
                }
                &.t-active {
                    color: base.$color-primary-default;
                    background-color: base.$color-primary-light-4;
                    padding: 0 base.unit-rem-calc(24px);
                    .c-pmt-icon,
                    .c-pmt-title {
                        color: base.$color-primary-default;
                    }
                }
                &.t-disabled {
                    cursor: not-allowed;
                    color: base.$color-grey-light-4;
                    .c-pmt-icon,
                    .c-pmt-title {
                        cursor: not-allowed;
                        color: base.$color-grey-light-4;
                    }
                    @include base.respond-to("hover") {
                        &:hover {
                            padding: 0 base.unit-rem-calc(12px);
                            background: transparent;
                            color: base.$color-grey-light-4;
                            .c-pmt-icon,
                            .c-pmt-title {
                                color: base.$color-grey-light-4;
                            }
                        }
                    }
                }
            }
            .c-hni-icon {
                flex: 0 0 auto;
                @include base.svg-icon("default-18");
                color: base.$color-grey-dark-1;
            }
            .c-hni-title {
                flex: 0 0 auto;
                @include base.typo-header(
                    $size: 14px,
                    $line-height: base.unit-rem-calc(20px)
                );
                color: base.$color-grey-dark-1;
                white-space: nowrap;
            }
        }

        .c-hn-user-profile {
            margin-left: auto;

            .c-hnup-pill {
                --pill-bg: var(--clr-primary-050, #d0d8ee);
                --pill-fg: var(--clr-primary-600, #2563eb);
                --pill-border: var(--clr-neutral-200, #d0d8ee);

                display: inline-flex;
                align-items: center;
                height: base.unit-rem-calc(40px);
                padding: 0 base.unit-rem-calc(18px) 0 base.unit-rem-calc(12px);
                margin-right: base.unit-rem-calc(12px);
                background: var(--pill-bg);
                border-radius: 32px 0 0 32px;
                font: 600 14px/1 var(--font-sans, "Inter", sans-serif);
                color: var(--pill-fg);
                cursor: pointer;
                position: relative;
                transition: box-shadow 0.15s;
            }
            .c-hnup-pill:focus-visible {
                outline: 2px solid var(--pill-fg);
            }

            .c-hnup-pill:hover {
                box-shadow: 0 0 0 2px var(--pill-bg) inset;
            }

            .c-hnup-text {
                margin: 0 base.unit-rem-calc(24px) 0 base.unit-rem-calc(24px);
            }

            .c-hnup-icon {
                flex: 0 0 40px;
                width: base.unit-rem-calc(40px);
                height: base.unit-rem-calc(40px);
                margin-right: base.unit-rem-calc(-40px);
                border: 1px solid var(--pill-border);
                border-radius: 50%;
                display: grid;
                place-content: center;
                box-sizing: border-box;
                background: #fff;
                z-index: 10;
            }
            .c-hnup-icon svg {
                width: base.unit-rem-calc(18px);
                height: base.unit-rem-calc(18px);
                color: var(--pill-fg);
            }
        }
    }

    .c-container-scrollable {
        display: flex;
        flex: 1;
        overflow: hidden;
    }
    .c-cs-tab-sections {
        display: flex;
        flex: 1;
        overflow: auto;
    }

    .m-company-info {
        display: flex;
        padding: 112px 24px 40px 24px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        align-self: stretch;
        h4 {
            color: #000;
            font-variant-numeric: slashed-zero;
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Barlow, sans-serif;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 140%;
        }
        .c-ci-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        .c-ci-block {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 16px;
            width: 100%;
            min-width: base.unit-rem-calc(340px);
            .c-ci-text {
                flex: 1 1 auto;
                min-width: 0;
                h6 {
                    color: var(--gray-dark-4, #1f252c);
                    font-feature-settings:
                        "liga" off,
                        "clig" off;
                    font-family: Barlow, sans-serif;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 140%;
                }
            }
            .c-action-icon {
                display: flex;
                padding: 10px;
                justify-content: center;
                align-items: center;
                gap: 8px;
                border-radius: var(--border-radius, 100px);
                border: 1px solid var(--Secondary-Stroke, #e1e8f0);
                background: var(--Secondary-Background, #fff);
                box-shadow:
                    0 4px 8px -4px rgba(119, 141, 166, 0.2),
                    0 1px 8px -8px rgba(119, 141, 166, 0.2);
                svg,
                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }
        .c-ci-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--gray‑500);
            margin-bottom: 6px;
        }
        .c-ci-field {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            position: relative;
            .c-cif-meta {
                flex: 1 1 0;
                min-width: 0;
                display: flex;
                flex-direction: column;
            }
            .c-cif-label {
                color: var(--gray-dark-4, #1f252c);
                font-feature-settings:
                    "liga" off,
                    "clig" off;
                font-family: Roboto, sans-serif;
                font-size: 13px;
                font-style: normal;
                font-weight: 500;
                line-height: 20px;
            }
            .c-cif-value {
                color: var(--gray-dark-2, #435162);
                font-feature-settings:
                    "liga" off,
                    "clig" off;
                font-family: Roboto, sans-serif;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
            }
        }
        .c-salesman {
            flex: 1;
            gap: 12px;
            align-items: flex-start;
            margin: 12px 0;
            .c-s-wrapper {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            .c-sw-avatar {
                display: flex;
                justify-content: center;
                align-items: center;
                width: var(--avatar-size, 40px);
                height: var(--avatar-size, 40px);
                border-radius: 40px;
                background: lightgray 50% / cover no-repeat;
                box-shadow:
                    0 1px 3px 0 rgba(119, 141, 166, 0.08),
                    0 1px 2px 0 rgba(119, 141, 166, 0.04);
            }
            .c-sw-name {
                font-weight: 600;
                color: var(--gray-700);
            }
        }
    }
}

.m-pages {
    flex: 1;
    display: flex;
    overflow: hidden;
}
.c-page {
    display: flex;
    flex: 1;

    .c-p-column {
        flex: 1;
        min-width: 260px;
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: 0;
    }
}

.c-card {
    background: #fff;
    border: 1px solid var(--gray‑100);
    display: flex;
    flex-direction: column;
    flex: 1;
    border-top: none;
    border-right: none;
}

.c-card:first-child {
}

.c-card > *:last-child {
    flex: 1;
}

.c-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid var(--gray‑100);
}
.c-card-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray‑700);
}

.c-see-all {
    font-size: 12px;
    font-weight: 500;
    color: var(--gray‑700);
    padding: 4px 8px;
    border: 1px solid var(--gray‑100);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}
.c-see-all:hover {
    background: var(--gray‑50);
}

.c-empty {
    padding: 32px 16px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    min-height: 180px;
    justify-content: center;

    .c-empty img {
        opacity: 0.55;
    }
    .c-empty .title {
        font-weight: 600;
        color: var(--gray‑700);
    }
    .c-empty .subtitle {
        font-size: 12px;
        color: var(--gray‑500);
        line-height: 1.3;
    }
}

// Card content styling
.c-card-content {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0;
}

// Appointment item styling
.c-appointment-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-bottom: 1px solid var(--gray‑100);
    transition: background-color 0.2s ease;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: var(--gray‑25);
    }

    .c-ai-date {
        flex: 0 0 auto;
        text-align: center;
        padding: 8px;
        border-radius: 8px;
        background: var(--blue‑50);
        min-width: 48px;

        .c-aid-month {
            font-size: 10px;
            font-weight: 600;
            color: var(--blue‑500);
            text-transform: uppercase;
            line-height: 1;
        }

        .c-aid-day {
            font-size: 18px;
            font-weight: 700;
            color: var(--gray‑700);
            line-height: 1;
            margin-top: 2px;
        }
    }

    .c-ai-details {
        flex: 1;
        min-width: 0;

        .c-aid-type {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray‑700);
            margin-bottom: 2px;
        }

        .c-aid-time {
            font-size: 12px;
            color: var(--gray‑500);
            margin-bottom: 2px;
        }

        .c-aid-location {
            font-size: 12px;
            color: var(--gray‑500);
        }
    }

    .c-ai-status {
        flex: 0 0 auto;

        .c-ais-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.c-status-scheduled {
                background: #e8f5e8;
                color: #2d7d32;
            }

            &.c-status-confirmed {
                background: #e3f2fd;
                color: #1976d2;
            }

            &.c-status-pending {
                background: #fff3e0;
                color: #f57c00;
            }
        }
    }
}

// Bid item styling
.c-bid-item {
    padding: 16px;
    border-bottom: 1px solid var(--gray‑100);
    transition: background-color 0.2s ease;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: var(--gray‑25);
    }

    .c-bi-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .c-bih-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray‑700);
        }

        .c-bih-id {
            font-size: 12px;
            color: var(--gray‑500);
            font-family: monospace;
        }
    }

    .c-bi-details {
        margin-bottom: 12px;

        .c-bid-address {
            font-size: 12px;
            color: var(--gray‑500);
            margin-bottom: 4px;
        }

        .c-bid-meta {
            .c-bm-total {
                font-size: 13px;
                color: var(--gray‑700);

                strong {
                    color: var(--blue‑500);
                    font-weight: 700;
                }
            }
        }
    }

    .c-bi-actions {
        display: flex;
        gap: 8px;
        align-items: center;

        .c-bia-btn {
            border: 1px solid var(--gray‑300);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
            color: var(--gray‑700);

            &:hover {
                border-color: var(--blue‑500);
                color: var(--blue‑500);
            }

            &.c-bia-view {
                flex: 1;
            }

            &.c-bia-download {
                padding: 6px 8px;
                display: flex;
                align-items: center;
                justify-content: center;

                .c-bia-icon {
                    fill: currentColor;
                }
            }
        }
    }
}

// Invoice item styling
.c-invoice-item {
    padding: 16px;
    border-bottom: 1px solid var(--gray‑100);
    transition: background-color 0.2s ease;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: var(--gray‑25);
    }

    .c-ii-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .c-iih-type {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray‑700);
        }

        .c-iih-status {
            .c-iis-badge {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;

                &.c-status-open {
                    background: #fff3e0;
                    color: #f57c00;
                }

                &.c-status-paid {
                    background: #e8f5e8;
                    color: #2d7d32;
                }

                &.c-status-overdue {
                    background: #ffebee;
                    color: #d32f2f;
                }
            }
        }
    }

    .c-ii-details {
        .c-iid-date {
            font-size: 12px;
            color: var(--gray‑500);
            margin-bottom: 6px;
        }

        .c-iid-amounts {
            margin-bottom: 6px;

            .c-iida-total,
            .c-iida-remaining {
                font-size: 13px;
                color: var(--gray‑700);
                margin-bottom: 2px;

                strong {
                    color: var(--blue‑500);
                    font-weight: 700;
                }
            }
        }

        .c-iid-number {
            font-size: 12px;
            color: var(--gray‑500);
            font-family: monospace;
        }
    }
}

// Upload items styling
.c-uploads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    padding: 16px;
}

.c-upload-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    background: var(--gray‑100);

    .c-ui-thumbnail {
        position: relative;
        width: 100%;
        height: 100%;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.2s ease;
        }

        .c-ui-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            padding: 8px 6px 4px;
            opacity: 0;
            transition: opacity 0.2s ease;

            .c-uio-date {
                font-size: 10px;
                color: white;
                font-weight: 500;
            }
        }
    }

    &:hover {
        .c-ui-thumbnail {
            img {
                transform: scale(1.05);
            }

            .c-ui-overlay {
                opacity: 1;
            }
        }
    }
}

@media (max-width: 1024px) {
    .c-p-column {
        min-width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--gray‑100);
    }
    .c-p-column:last-child {
        border-bottom: none;
    }

    .c-uploads-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 6px;
        padding: 12px;
    }

    .c-appointment-item {
        padding: 12px;

        .c-ai-date {
            min-width: 40px;
            padding: 6px;

            .c-aid-day {
                font-size: 16px;
            }
        }
    }

    .c-bid-item,
    .c-invoice-item {
        padding: 12px;
    }
}
