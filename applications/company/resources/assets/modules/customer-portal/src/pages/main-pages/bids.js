'use strict';

import Page from '@ca-package/router/src/page';
import bids_tpl from '@cam-customer-portal-tpl/pages/main-pages/bids.hbs';

/**
 * Bids page for customer portal
 *
 * @memberof module:CustomerPortal/Pages/MainPages
 */
export class BidsPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            bids: []
        });
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        const { customer_data } = this.state;

        await super.load(request, next);
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return bids_tpl({
            customer_data: this.state.customer_data,
            bids: this.state.bids
        });
    }
}