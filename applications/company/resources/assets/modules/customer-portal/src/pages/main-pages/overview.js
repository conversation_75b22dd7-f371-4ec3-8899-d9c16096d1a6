'use strict';

import Page from '@ca-package/router/src/page';
import overview_tpl from '@cam-customer-portal-tpl/pages/main-pages/overview.hbs';
import {Client} from '../../lib/client';
import {createErrorMessage} from '@cas-notification-toast-js/message/error';

/**
 * Overview page for customer portal
 *
 */
export class OverviewPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            data: {},
            loading: true,
            loaded: false,
            error: null
        });

        this.client = new Client(this.state.customer_data?.customerUUID);
    }

    async boot(root) {
        super.boot(root);
    }

    /**
     * Fetch all data from API endpoints
     *
     * @returns {Promise<void>}
     */
    async fetchAllData() {
        try {
            this.state.loading = true;
            this.state.error = null;

            // Fetch all data concurrently for better performance
            const [appointments, bids, invoices, uploads] = await Promise.all([
                this.client.fetchData('appointments'),
                this.client.fetchData('bids'),
                this.client.fetchData('invoices'),
                this.client.fetchData('uploads')
            ]);

            console.log({appointments, bids, invoices, uploads});

            // Update state with fetched data
            Object.assign(this.state.data, {
                appointments,
                // bids,
                // invoices,
                // uploads
                bids: [],
                invoices: [],
                uploads:[]
            });

            this.state.loaded = true;
            //console.log('Data loaded successfully:', this.state.data);
        } catch (error) {
            console.error('Error fetching overview data:', error);
            this.state.error = error;

            // Show user-friendly error message
            const message = createErrorMessage('Unable to load overview data. Please refresh the page and try again.');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Update the page content after data changes
     */
    updateContent() {
        if (this.elem && this.elem.root) {
            this.elem.root.html(this.render());
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        // Show loader if parent has one
        if (this.state.parent && typeof this.state.parent.showLoader === 'function') {
            this.state.parent.showLoader();
        }

        try {
            await this.fetchAllData();
            // Update content after data is loaded
            this.updateContent();
        } finally {
            // Hide loader
            if (this.state.parent && typeof this.state.parent.hideLoader === 'function') {
                this.state.parent.hideLoader();
            }
        }

        await super.load(request, next);
    }

    /**
     * Refresh page data
     *
     * @param {object} request
     */
    async refresh(request) {
        // Clear existing data
        this.state.data = {};
        this.state.loaded = false;
        this.state.error = null;

        // Fetch fresh data
        await this.fetchAllData();

        // Re-render the page
        this.updateContent();
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return overview_tpl({
            customer_data: this.state.customer_data,
            appointments: this.state.data.appointments,
            bids: this.state.data.bids,
            invoices: this.state.data.invoices,
            uploads: this.state.data.uploads,
            loading: this.state.loading,
            loaded: this.state.loaded,
            error: this.state.error
        });
    }
}
