'use strict';

import Page from '@ca-package/router/src/page';
import overview_tpl from '@cam-customer-portal-tpl/pages/main-pages/overview.hbs';
import {Client} from '../../lib/client';
import {createErrorMessage} from '@cas-notification-toast-js/message/error';

/**
 * Overview page for customer portal
 *
 */
export class OverviewPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            data: {},
            loading: true,
            loaded: false,
            error: null
        });

        this.client = new Client(this.state.customer_data?.customerUUID);
        this.registerHandlebarsHelpers();
    }

    async boot(root) {
        super.boot(root);
    }

    /**
     * Register Handlebars helpers for data formatting
     */
    registerHandlebarsHelpers() {
        // Check if Handlebars is available globally
        if (typeof window.Handlebars === 'undefined') {
            console.warn('Handlebars not available globally, skipping helper registration');
            return;
        }

        const Handlebars = window.Handlebars;

        // Format date helpers
        Handlebars.registerHelper('formatDate', function(date) {
            if (!date) return '';
            const d = new Date(date);
            return d.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            });
        });

        Handlebars.registerHelper('formatMonth', function(date) {
            if (!date) return '';
            const d = new Date(date);
            return d.toLocaleDateString('en-US', { month: 'short' });
        });

        Handlebars.registerHelper('formatDay', function(date) {
            if (!date) return '';
            const d = new Date(date);
            return d.getDate();
        });

        Handlebars.registerHelper('formatTime', function(date) {
            if (!date) return '';
            const d = new Date(date);
            return d.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        });

        // Format currency
        Handlebars.registerHelper('formatCurrency', function(amount) {
            if (!amount) return '0.00';
            return parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        });

        // Status class helper
        Handlebars.registerHelper('statusClass', function(status) {
            if (!status) return '';
            return `c-status-${status.toLowerCase().replace(/\s+/g, '-')}`;
        });
    }

    /**
     * Process appointments data for display
     *
     * @param {Array} appointments
     * @returns {Array}
     */
    processAppointments(appointments) {
        if (!Array.isArray(appointments)) return [];

        return appointments.slice(0, 3).map(appointment => ({
            ...appointment,
            type: appointment.type || 'Evaluation',
            status: appointment.status || 'Scheduled',
            location: appointment.location || 'TBD'
        }));
    }

    /**
     * Process bids data for display
     *
     * @param {Array} bids
     * @returns {Array}
     */
    processBids(bids) {
        if (!Array.isArray(bids)) return [];

        return bids.slice(0, 2).map(bid => ({
            ...bid,
            projectName: bid.projectName || bid.name || 'Project',
            bidId: bid.bidId || bid.id,
            address: bid.address || 'Address TBD',
            total: bid.total || bid.amount || 0
        }));
    }

    /**
     * Process invoices data for display
     *
     * @param {Array} invoices
     * @returns {Array}
     */
    processInvoices(invoices) {
        if (!Array.isArray(invoices)) return [];

        return invoices.slice(0, 3).map(invoice => ({
            ...invoice,
            type: invoice.type || 'Deposit',
            status: invoice.status || 'Open',
            total: invoice.total || invoice.amount || 0,
            remainingBalance: invoice.remainingBalance || invoice.total || invoice.amount || 0,
            invoiceNumber: invoice.invoiceNumber || invoice.number || 'N/A'
        }));
    }

    /**
     * Process uploads data for display
     *
     * @param {Array} uploads
     * @returns {Array}
     */
    processUploads(uploads) {
        if (!Array.isArray(uploads)) return [];

        return uploads.slice(0, 10).map(upload => ({
            ...upload,
            thumbnailUrl: upload.thumbnailUrl || upload.url || '/placeholder-image.jpg',
            filename: upload.filename || upload.name || 'Untitled'
        }));
    }

    /**
     * Fetch all data from API endpoints
     *
     * @returns {Promise<void>}
     */
    async fetchAllData() {
        try {
            this.state.loading = true;
            this.state.error = null;

            // Fetch all data concurrently for better performance
            const [appointments, bids, invoices, uploads] = await Promise.all([
                this.client.fetchData('appointments'),
                this.client.fetchData('bids'),
                this.client.fetchData('invoices'),
                this.client.fetchData('uploads')
            ]);

            console.log({appointments, bids, invoices, uploads});

            // Update state with fetched data
            Object.assign(this.state.data, {
                appointments: this.processAppointments(appointments),
                bids: this.processBids(bids),
                invoices: this.processInvoices(invoices),
                uploads: this.processUploads(uploads)
            });

            this.state.loaded = true;
            //console.log('Data loaded successfully:', this.state.data);
        } catch (error) {
            console.error('Error fetching overview data:', error);
            this.state.error = error;

            // Show user-friendly error message
            const message = createErrorMessage('Unable to load overview data. Please refresh the page and try again.');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Update the page content after data changes
     */
    updateContent() {
        if (this.elem && this.elem.root) {
            this.elem.root.html(this.render());
            this.bindEventHandlers();
        }
    }

    /**
     * Bind event handlers for card interactions
     */
    bindEventHandlers() {
        if (!this.elem || !this.elem.root) return;

        // Bid view buttons
        this.elem.root.find('.c-bia-btn.c-bia-view').off('click').on('click', (e) => {
            const bidId = $(e.currentTarget).data('bid-id');
            this.handleViewBid(bidId);
        });

        // Bid download buttons
        this.elem.root.find('.c-bia-btn.c-bia-download').off('click').on('click', (e) => {
            const bidId = $(e.currentTarget).data('bid-id');
            this.handleDownloadBid(bidId);
        });
    }

    /**
     * Handle view bid action
     *
     * @param {string} bidId
     */
    handleViewBid(bidId) {
        // Navigate to bid details or open modal
        this.router.navigate(`#/bids/${bidId}`);
    }

    /**
     * Handle download bid action
     *
     * @param {string} bidId
     */
    handleDownloadBid(bidId) {
        // Trigger bid download
        console.log('Downloading bid:', bidId);
        // Implementation would depend on API endpoint
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        // Show loader if parent has one
        if (this.state.parent && typeof this.state.parent.showLoader === 'function') {
            this.state.parent.showLoader();
        }

        try {
            await this.fetchAllData();
            // Update content after data is loaded
            this.updateContent();
        } finally {
            // Hide loader
            if (this.state.parent && typeof this.state.parent.hideLoader === 'function') {
                this.state.parent.hideLoader();
            }
        }

        await super.load(request, next);
    }

    /**
     * Refresh page data
     *
     * @param {object} request
     */
    async refresh(request) {
        // Clear existing data
        this.state.data = {};
        this.state.loaded = false;
        this.state.error = null;

        // Fetch fresh data
        await this.fetchAllData();

        // Re-render the page
        this.updateContent();
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return overview_tpl({
            customer_data: this.state.customer_data,
            appointments: this.state.data.appointments,
            bids: this.state.data.bids,
            invoices: this.state.data.invoices,
            uploads: this.state.data.uploads,
            loading: this.state.loading,
            loaded: this.state.loaded,
            error: this.state.error
        });
    }
}
